import I18N from '@/i18n';
import type { ButtonProps } from 'antd';
import { Button, Col, Dropdown, Row, Typography } from 'antd';
import styles from './index.less';
import Placeholder from '@/components/Common/Placeholder';
import type { OverflowProps } from 'rc-overflow';
import Overflow from 'rc-overflow';
import type { CSSProperties, HTMLProps, MouseEventHandler, ReactNode } from 'react';
import { useEffect } from 'react';
import React, { useCallback, useRef, useState } from 'react';
import classNames from 'classnames';
import IconFontIcon from '@/components/Common/IconFontIcon';
import TagItem from '@/components/Common/TagManage/TagItem';
import styled from 'styled-components';
import buttonStyles from '@/style/button.less';

interface DropdownWrapperProps {
  data: any[];
  renderItem: (item: any, props?: HTMLProps<any>) => JSX.Element;
  renderRestItem?: (item: any, props?: HTMLProps<any>) => JSX.Element;
  itemKey?: string | number;
}

type Props = OverflowProps<any> & DropdownWrapperProps;

function useRenderDropdown(props: Omit<DropdownWrapperProps, 'data'>) {
  const { renderRestItem, renderItem, itemKey = 'id' } = props;
  return useCallback(
    (items: any[]) => {
      const children: ReactNode[] = [];
      items.forEach((item) => {
        const node = (renderRestItem || renderItem)(item);
        const menuNode = React.cloneElement(node);
        children.push({ key: item[itemKey], label: menuNode });
      });
      return (
        <Dropdown
          placement="bottom"
          trigger={['click']}
          menu={{
            items: children,
            className: styles.menus,
          }}
        >
          <IconFontIcon
            iconName="gengduo_24"
            className={styles.more}
            onClick={(e) => {
              e.stopPropagation();
            }}
          />
        </Dropdown>
      );
    },
    [itemKey, renderItem, renderRestItem],
  );
}
/**
 * 显示几个，然后多的打点
 * @param props
 * @constructor
 */
const MoreDropdown = (props: Props) => {
  const {
    data = [],
    maxCount = 'responsive',
    renderItem,
    itemKey = 'id',
    renderRestItem,
    className,
    ...others
  } = props;
  const ref = useRef<HTMLDivElement>();

  const renderDropdown = useRenderDropdown({
    itemKey,
    renderItem,
    renderRestItem,
  });

  if (!data?.length) {
    return <Placeholder>{I18N.t('无数据')}</Placeholder>;
  }

  return (
    <div className={classNames(styles.moreDropdownWrapper, className, 'more-dropdown-wrapper')}>
      <Overflow
        ref={ref}
        data={data}
        itemKey={itemKey}
        maxCount={maxCount}
        renderItem={renderItem}
        renderRest={(items) => {
          if (ref.current) {
            if (items.length) {
              ref.current.classList.add('ellipsis');
            } else {
              ref.current.classList.remove('ellipsis');
            }
          }
          return renderDropdown(items);
        }}
        {...others}
      />
    </div>
  );
};

export const DropdownWrapper: React.FC<DropdownWrapperProps> = (props) => {
  const { data, renderItem, renderRestItem, itemKey = 'id' } = props;
  const [ellipsis, onEllipsis] = useState(false);
  const renderDropdown = useRenderDropdown({
    renderItem,
    renderRestItem,
    itemKey,
  });
  return (
    <Row className={classNames(styles.overflowWrapper, { ellipsis })}>
      <Col style={{ flex: 1, overflow: 'hidden' }}>
        <Typography.Paragraph style={{ marginBottom: 0 }} ellipsis={{ rows: 1, onEllipsis }}>
          {data.map((item) => {
            return React.cloneElement(renderItem(item), {
              key: item[itemKey],
            });
          })}
        </Typography.Paragraph>
      </Col>
      <Col style={{ height: 24, flex: '0 0 24px' }}>{renderDropdown(data)}</Col>
    </Row>
  );
};

export default MoreDropdown;
export const TagMoreDropdown = (props: {
  tags?: API.TagDto[];
  style?: CSSProperties;
  className?: string;
  overlayClassName?: string;
  tagStyle?: CSSProperties;
}) => {
  const { tags, style, className, overlayClassName, tagStyle } = props;
  if (!tags?.length) {
    return <Placeholder />;
  }
  return (
    <MoreDropdown
      style={style}
      className={classNames(styles.tagMoreWrapper, className)}
      overlayClassName={classNames(styles.tagMoreOverlay, overlayClassName)}
      data={tags}
      maxCount={'responsive'}
      renderItem={(tag) => {
        return <TagItem tag={tag} key={tag.id} style={tagStyle} />;
      }}
    />
  );
};

export const _StyledOverflow = styled(Overflow)`
  position: relative;
  flex: 1;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  margin-right: 24px;

  .rc-overflow-item {
    display: inline-block;
    overflow: hidden;
    vertical-align: bottom;

    &:not(.rc-overflow-item-rest) {
      padding-right: 8px;
    }
  }
`;

export const StyledOverflowMoreDropdown = styled(Dropdown)`
  && {
    .iconfont {
      font-size: 14px;
    }
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      //width: 160px;
      padding: 0;
      color: #0f7cf4;
      &:hover {
        background: #0f7cf4;
      }
    }
    .ant-dropdown-menu-title-content {
      > * {
        height: auto;
        padding: 8px;
        text-align: left;
      }
    }
    .ant-dropdown-menu-submenu-arrow-icon {
      color: inherit !important;
    }
  }
`;
export type OverflowItem = {
  node: (props: any) => ReactNode;
  key: string;
};
export const StyledOverflow = (props: OverflowProps<OverflowItem> & { disabled?: boolean }) => {
  const { disabled, itemWidth = 110, className, ...rest } = props;
  const [ellipsis, setEllipsis] = useState(false);
  return (
    <_StyledOverflow
      className={classNames({ ellipsis }, className)}
      maxCount={'responsive'}
      itemWidth={itemWidth}
      renderItem={(item: OverflowItem) => {
        if (disabled) {
          return item.node({
            type: 'primary',
            ghost: true,
            style: {
              minWidth: itemWidth,
            },
            className: buttonStyles.disabled,
            onClick() {},
          });
        }
        return item.node({
          type: 'primary',
          ghost: true,
          style: {
            minWidth: itemWidth,
          },
        });
      }}
      renderRest={(items: OverflowItem[]) => {
        setEllipsis(items.length !== 0);
        return (
          <StyledOverflowMoreDropdown
            placement="topCenter"
            trigger={['hover']}
            menu={{
              items: items.map((item: OverflowItem) => {
                if (disabled) {
                  return {
                    label: item.node({
                      type: 'link',
                      className: buttonStyles.disabledBtnLink,
                      block: true,
                      size: 'small',
                      style: {
                        textAlign: 'left',
                      },
                      onClick() {},
                    }),
                    key: item.key,
                  };
                }
                return {
                  label: item.node({
                    type: 'link',
                    block: true,
                    size: 'small',
                    style: {
                      textAlign: 'left',
                    },
                  }),
                  key: item.key,
                };
              }),
            }}
          >
            <Button
              className={disabled ? buttonStyles.disabled : ''}
              type={'primary'}
              icon={<IconFontIcon iconName="gengduo_24" />}
              ghost
              style={{ width: 22 }}
            />
          </StyledOverflowMoreDropdown>
        );
      }}
      {...rest}
    />
  );
};
const StyledHack = styled(Overflow)`
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  gap: 0 !important;
  margin-right: 0 !important;
  &.ellipsis {
    // 溢出情况下
    .rc-overflow-item-rest {
      button {
        border-top-right-radius: 3px !important;
        border-bottom-right-radius: 3px !important;
      }
    }
  }
  &:not(.ellipsis) {
    .rc-overflow-item:nth-last-of-type(1) {
      button {
        border-top-right-radius: 3px !important;
        border-bottom-right-radius: 3px !important;
      }
    }
  }
  .rc-overflow-item {
    padding-right: 0 !important;
    overflow: hidden;
    button {
      width: 100% !important;
      overflow: hidden;
      border-radius: 0 !important;
    }
    &:not(.rc-overflow-item-rest) {
      flex: 1;
    }
    &:not(:nth-of-type(1)) {
      button {
        border-left-color: transparent;
      }
    }
    &:nth-of-type(1) {
      button {
        border-top-left-radius: 3px !important;
        border-bottom-left-radius: 3px !important;
      }
    }
  }
`;

type ItemProps = {
  node: (props: ButtonProps & { overflow?: boolean }) => ReactNode;
  key: string;
};
export const ResponsiveOverflow = (props: OverflowProps<ItemProps> & { disabled?: boolean }) => {
  const { disabled, itemWidth = 130, maxCount: _maxCount, className, ...rest } = props;
  const [ellipsis, setEllipsis] = useState(false);
  const [maxCount, setMaxCount] = useState(_maxCount || 0);
  const ref = useRef();
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const _count = Math.floor(entries[0].contentRect.width / itemWidth);
      setMaxCount(_count);
    });
    if (ref.current) {
      observer.observe(ref.current);
    }
    return () => {
      observer.disconnect();
    };
  }, [itemWidth]);

  return (
    <div style={{ overflow: 'hidden' }} ref={ref}>
      <StyledHack
        className={classNames({ ellipsis }, className)}
        maxCount={maxCount}
        itemWidth={itemWidth}
        renderItem={(item: ItemProps) => {
          if (disabled) {
            return item.node({
              type: 'primary',
              ghost: true,
              className: buttonStyles.disabled,
              overflow: false,
              onClick() {},
            });
          }
          return item.node({
            type: 'primary',
            ghost: true,
            overflow: false,
          });
        }}
        renderRest={(items) => {
          setEllipsis(items.length !== 0);
          return (
            <StyledOverflowMoreDropdown
              trigger={['hover']}
              menu={{
                items: items.map((item: ItemProps) => {
                  if (disabled) {
                    return {
                      label: item.node({
                        type: 'link',
                        className: buttonStyles.disabledBtnLink,
                        overflow: true,
                        block: true,
                        size: 'small',
                        style: {
                          textAlign: 'left',
                        },
                        onClick() {},
                      }),
                      key: item.key,
                    };
                  }
                  return {
                    label: item.node({
                      type: 'link',
                      block: true,
                      size: 'small',
                      overflow: true,
                      style: {
                        textAlign: 'left',
                      },
                    }),
                    key: item.key,
                  };
                }),
              }}
            >
              <Button
                className={disabled ? buttonStyles.disabled : ''}
                type={'primary'}
                icon={<IconFontIcon iconName="gengduo_24" />}
                ghost
                style={{ width: 22 }}
              />
            </StyledOverflowMoreDropdown>
          );
        }}
        {...rest}
      />
    </div>
  );
};
const StyledContainer = styled.div``;
export const ResponsiveOptions = (props: {
  itemWidth: number;
  gap: number;
  items: {
    key: string;
    label: ReactNode;
    icon: ReactNode;
    onClick: MouseEventHandler<MouseEvent>;
  }[];
}) => {
  const { itemWidth, gap, items } = props;
  const [ellipsis, setEllipsis] = useState(false);
  const ref = useRef<HTMLDivElement>();
  const [maxCount, setMaxCount] = useState(items.length);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const _count = Math.floor(entries[0].contentRect.width / itemWidth);
      setMaxCount(_count);
    });
    if (ref.current) {
      observer.observe(ref.current);
    }
    return () => {
      observer.disconnect();
    };
  }, [itemWidth]);

  return (
    <div style={{ overflow: 'hidden' }} ref={ref}>
      <StyledContainer></StyledContainer>
    </div>
  );
};
