import { useEffect, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import type { InputRef } from 'antd';
import { Form, Input, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { trimValues } from '@/utils/utils';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import _ from 'lodash';

const InputADCodeModal = (
  props: GhostModalWrapperComponentProps & {
    onSubmit: (value: string) => Promise<any>;
    value: string | undefined;
  },
) => {
  const { value, onSubmit, modalProps } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      const { value: adCode } = trimValues(values);
      await onSubmit(adCode || '');
      setOpen(false);
      message.success('更新成功');
    },
    {
      manual: true,
    },
  );
  const ref = useRef<InputRef>();
  useEffect(() => {
    setTimeout(() => {
      ref.current?.focus();
    }, 300);
  }, []);
  return (
    <DMModal
      title={value ? '修改投流码' : '录入投流码'}
      open={open}
      onOk={submit}
      bodyStyle={{ paddingBottom: 0 }}
      confirmLoading={loading}
      onCancel={() => {
        setOpen(false);
      }}
      {...modalProps}
    >
      <Form form={form}>
        <Form.Item
          name={'value'}
          initialValue={value || ''}
          rules={[
            {
              validator(_rule, _value) {
                const text = _.trim(_value || '');
                if (!text) {
                  return Promise.resolve();
                }
                if (!/^#[A-Za-z0-9+/=]+$/.test(text)) {
                  return Promise.reject(new Error('投流码格式不正确'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input.TextArea style={{ height: 65, resize: 'none' }} ref={ref} />
        </Form.Item>
      </Form>
    </DMModal>
  );
};
export default InputADCodeModal;
